CREATE TABLE IF NOT EXISTS servers (
    server_id BIGINT PRIMARY KEY,
    muterole BIGINT,
    antiinvite BOOLEAN DEFAULT False,
    reassign B<PERSON><PERSON><PERSON><PERSON> DEFAULT True,
    autoroles BIGINT [] DEFAULT '{}',
    profanities TEXT [] DEFAULT '{}'
);
CREATE TABLE IF NOT EXISTS prefixes (
    server_id BIGINT,
    prefix VARCHAR(30),
    UNIQUE (server_id, prefix)
);
CREATE TABLE IF NOT EXISTS logs (
    server_id BIGINT PRIMARY KEY,
    avatars BOOLEAN DEFAULT True,
    channels BOOLEAN DEFAULT True,
    emojis <PERSON><PERSON><PERSON>EAN DEFAULT True,
    invites BOOLEAN DEFAULT True,
    joins BOOLEAN DEFAULT True,
    leaves BOOLEAN DEFAULT True,
    messages BOOLEAN DEFAULT True,
    moderation BOOLEAN DEFAULT True,
    nicknames BOOLEAN DEFAULT True,
    usernames <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT True,
    roles BOOLEA<PERSON> DEFAULT True,
    server <PERSON><PERSON><PERSON><PERSON>N DEFAULT True,
    voice BOOLEAN DEFAULT True
);
CREATE TABLE IF NOT EXISTS log_data (
    server_id BIGINT PRIMARY KEY,
    channel_id BIGINT,
    webhook_id BIGINT,
    webhook_token TEXT,
    entities BIGINT [] DEFAULT '{}'
);
CREATE TABLE IF NOT EXISTS command_config (
    id BIGSERIAL PRIMARY KEY,
    server_id BIGINT,
    entity_id BIGINT,
    command TEXT,
    insertion TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);
CREATE UNIQUE INDEX IF NOT EXISTS command_config_idx ON command_config(entity_id, command);
CREATE TABLE IF NOT EXISTS plonks (
    id BIGSERIAL PRIMARY KEY,
    server_id BIGINT,
    entity_id BIGINT,
    insertion TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);
CREATE UNIQUE INDEX IF NOT EXISTS permissions_idx ON plonks(server_id, entity_id);
CREATE TABLE IF NOT EXISTS warns (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT,
    server_id BIGINT,
    reason TEXT,
    insertion TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);

-- Starboard configuration table
CREATE TABLE IF NOT EXISTS starboard_config (
    server_id BIGINT PRIMARY KEY,
    channel_id BIGINT,
    emoji TEXT DEFAULT '⭐',
    threshold INTEGER DEFAULT 3,
    color TEXT DEFAULT '#323339',
    locked BOOLEAN DEFAULT FALSE,
    allow_selfstar BOOLEAN DEFAULT FALSE,
    show_attachments BOOLEAN DEFAULT TRUE,
    show_jumpurl BOOLEAN DEFAULT TRUE,
    show_timestamp BOOLEAN DEFAULT TRUE
);

-- Starboard messages tracking
CREATE TABLE IF NOT EXISTS starboard_messages (
    id BIGSERIAL PRIMARY KEY,
    server_id BIGINT,
    original_message_id BIGINT,
    starboard_message_id BIGINT,
    channel_id BIGINT,
    author_id BIGINT,
    star_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    UNIQUE(server_id, original_message_id)
);

-- Starboard ignored entities (channels, users, roles)
CREATE TABLE IF NOT EXISTS starboard_ignored (
    id BIGSERIAL PRIMARY KEY,
    server_id BIGINT,
    entity_id BIGINT,
    entity_type TEXT, -- 'channel', 'user', 'role'
    UNIQUE(server_id, entity_id, entity_type)
);
CREATE TABLE IF NOT EXISTS tasks (
    id BIGSERIAL PRIMARY KEY,
    expires TIMESTAMP,
    created TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    event TEXT,
    extra jsonb DEFAULT '{}'::jsonb
);
CREATE TABLE IF NOT EXISTS invites (
    invitee BIGINT,
    inviter BIGINT,
    server_id BIGINT
);
CREATE TABLE IF NOT EXISTS servericons (
    server_id BIGINT,
    icon TEXT,
    first_seen TIMESTAMP
);
CREATE TABLE IF NOT EXISTS icons (
    hash TEXT PRIMARY KEY,
    url TEXT,
    msgid BIGINT,
    id bigint,
    size bigint,
    height bigint,
    width bigint,
    insertion TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);

-- Giveaways table
CREATE TABLE IF NOT EXISTS giveaways (
    id BIGSERIAL PRIMARY KEY,
    server_id BIGINT NOT NULL,
    channel_id BIGINT NOT NULL,
    message_id BIGINT UNIQUE,
    host_id BIGINT NOT NULL,
    prize TEXT NOT NULL,
    winners INTEGER DEFAULT 1,
    end_time TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    ended BOOLEAN DEFAULT FALSE,
    cancelled BOOLEAN DEFAULT FALSE,
    description TEXT,
    image_url TEXT,
    thumbnail_url TEXT,
    color TEXT DEFAULT '#323339',
    min_level INTEGER DEFAULT 0,
    max_level INTEGER DEFAULT NULL,
    min_account_age INTEGER DEFAULT 0,
    min_server_stay INTEGER DEFAULT 0,
    required_roles BIGINT[] DEFAULT '{}',
    winner_roles BIGINT[] DEFAULT '{}',
    additional_hosts BIGINT[] DEFAULT '{}'
);

-- Giveaway entries table
CREATE TABLE IF NOT EXISTS giveaway_entries (
    id BIGSERIAL PRIMARY KEY,
    giveaway_id BIGINT REFERENCES giveaways(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL,
    entered_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    UNIQUE(giveaway_id, user_id)
);

-- Giveaway winners table
CREATE TABLE IF NOT EXISTS giveaway_winners (
    id BIGSERIAL PRIMARY KEY,
    giveaway_id BIGINT REFERENCES giveaways(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL,
    won_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);