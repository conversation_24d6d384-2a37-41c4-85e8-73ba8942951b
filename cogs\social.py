import discord
import aiohttp
import re
from typing import Optional, Dict, Any

from discord.ext import commands

from utilities import decorators


class YouTubeSearchView(discord.ui.View):
    def __init__(self, videos: list, query: str):
        super().__init__(timeout=300)
        self.videos = videos
        self.query = query
        self.current_page = 0
        self.max_page = len(videos) - 1

    def get_current_url(self) -> str:
        """Get the current video URL for Discord's auto-embed"""
        return self.videos[self.current_page]["url"]

    def get_message_content(self) -> str:
        """Get message content with video URL for auto-embed"""
        video = self.videos[self.current_page]
        return f"{video['url']}\n**{video['author']['name']}**\n{video['title']}"

    async def update_message(self, interaction: discord.Interaction):
        """Update message content to change the embedded video"""
        content = self.get_message_content()

        # Edit the message with new content - Discord should update the embed
        await interaction.response.edit_message(content=content, view=self)

    @discord.ui.button(emoji="<:backward:1393199972366684191>", style=discord.ButtonStyle.blurple)
    async def previous_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        _ = button  # Suppress unused parameter warning
        if self.current_page > 0:
            self.current_page -= 1
            await self.update_message(interaction)
        else:
            await interaction.response.defer()

    @discord.ui.button(emoji="<:forward:1393199534040944761>", style=discord.ButtonStyle.blurple)
    async def next_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        _ = button  # Suppress unused parameter warning
        if self.current_page < self.max_page:
            self.current_page += 1
            await self.update_message(interaction)
        else:
            await interaction.response.defer()

    @discord.ui.button(emoji="<:shuffle:1396520272642838579>", style=discord.ButtonStyle.secondary)
    async def shuffle(self, interaction: discord.Interaction, button: discord.ui.Button):
        _ = button  # Suppress unused parameter warning
        import random
        random.shuffle(self.videos)
        self.current_page = 0
        await self.update_message(interaction)

    @discord.ui.button(emoji="<:lcross:1393191711848529932>", style=discord.ButtonStyle.red)
    async def close(self, interaction: discord.Interaction, button: discord.ui.Button):
        _ = button  # Suppress unused parameter warning
        await interaction.response.edit_message(view=None)


class GoogleSearchView(discord.ui.View):
    def __init__(self, results: list, query: str):
        super().__init__(timeout=300)
        self.results = results
        self.query = query
        self.current_page = 0
        self.max_page = len(results) - 1

    def create_embed(self, page: int) -> discord.Embed:
        embed = discord.Embed(
            title="Search Results",
            color=0x323339
        )

        # Show multiple results per page (3 results per page)
        start_idx = page * 3
        end_idx = min(start_idx + 3, len(self.results))

        for i in range(start_idx, end_idx):
            result = self.results[i]
            embed.add_field(
                name=result["title"],
                value=f"[{result['link']}]({result['link']})\n{result['description'][:150]}{'...' if len(result['description']) > 150 else ''}",
                inline=False
            )

        embed.set_footer(text=f"Page {page + 1}/{(len(self.results) + 2) // 3} of Search Results")
        return embed

    @discord.ui.button(emoji="<:backward:1393199972366684191>", style=discord.ButtonStyle.blurple)
    async def previous_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        _ = button  # Suppress unused parameter warning
        if self.current_page > 0:
            self.current_page -= 1
            embed = self.create_embed(self.current_page)
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(emoji="<:forward:1393199534040944761>", style=discord.ButtonStyle.blurple)
    async def next_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        _ = button  # Suppress unused parameter warning
        max_pages = (len(self.results) + 2) // 3
        if self.current_page < max_pages - 1:
            self.current_page += 1
            embed = self.create_embed(self.current_page)
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(emoji="<:shuffle:1396520272642838579>", style=discord.ButtonStyle.secondary)
    async def shuffle(self, interaction: discord.Interaction, button: discord.ui.Button):
        _ = button  # Suppress unused parameter warning
        import random
        random.shuffle(self.results)
        self.current_page = 0
        embed = self.create_embed(self.current_page)
        await interaction.response.edit_message(embed=embed, view=self)

    @discord.ui.button(emoji="<:lcross:1393191711848529932>", style=discord.ButtonStyle.red)
    async def close(self, interaction: discord.Interaction, button: discord.ui.Button):
        _ = button  # Suppress unused parameter warning
        await interaction.response.edit_message(view=None)


async def setup(bot):
    await bot.add_cog(Social(bot))


class CarbonModal(discord.ui.Modal, title="Create Carbon Image"):
    def __init__(self, cog):
        super().__init__()
        self.cog = cog

    code = discord.ui.TextInput(
        label="Enter your code",
        style=discord.TextStyle.paragraph,
        required=True,
        max_length=2000
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer()

        try:
            # Create carbon image link
            carbon_url = await self.cog.create_carbon_image(self.code.value)

            if carbon_url:
                embed = discord.Embed(
                    color=0x323339,
                    title="Your Carbon Image",
                    description=f"[Click here to view and download your Carbon image]({carbon_url})"
                )
                embed.add_field(
                    name="Instructions",
                    value="1. Click the link above\n2. The image will load automatically\n3. Right-click and save the image",
                    inline=False
                )
                await interaction.followup.send(embed=embed)
            else:
                # Fallback: Create a simple code block embed
                code_content = self.code.value
                if len(code_content) > 1900:
                    code_content = code_content[:1900] + "..."

                embed = discord.Embed(
                    color=0x323339,
                    title="Code Block",
                    description=f"```\n{code_content}\n```"
                )
                embed.set_footer(text="Carbon service unavailable - showing code block instead")
                await interaction.followup.send(embed=embed)

        except Exception:
            embed = discord.Embed(
                color=0xFF0000,
                title="Error",
                description="There was an error while processing your code."
            )
            await interaction.followup.send(embed=embed, ephemeral=True)


class CarbonView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=60)
        self.cog = cog

    @discord.ui.button(label="Create Carbon", style=discord.ButtonStyle.primary)
    async def create_carbon(self, interaction: discord.Interaction, button: discord.ui.Button):
        _ = button  # Suppress unused parameter warning
        modal = CarbonModal(self.cog)
        await interaction.response.send_modal(modal)


class PaginationView(discord.ui.View):
    def __init__(self, ctx, data, embed_generator, timeout=60):
        super().__init__(timeout=timeout)
        self.ctx = ctx
        self.data = data
        self.embed_generator = embed_generator
        self.current_index = 0
        
        # Disable buttons if only one item
        if len(data) <= 1:
            self.prev_button.disabled = True
            self.next_button.disabled = True
        else:
            self.prev_button.disabled = True

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.ctx.author.id:
            await interaction.response.send_message(
                "You can't use these buttons.", ephemeral=True
            )
            return False
        return True

    @discord.ui.button(label="◀ Previous", style=discord.ButtonStyle.secondary)
    async def prev_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        _ = button  # Suppress unused parameter warning
        self.current_index -= 1

        # Update button states
        self.prev_button.disabled = (self.current_index == 0)
        self.next_button.disabled = (self.current_index == len(self.data) - 1)

        embed = self.embed_generator(self.current_index)
        await interaction.response.edit_message(embed=embed, view=self)

    @discord.ui.button(label="Next ▶", style=discord.ButtonStyle.secondary)
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        _ = button  # Suppress unused parameter warning
        self.current_index += 1

        # Update button states
        self.prev_button.disabled = (self.current_index == 0)
        self.next_button.disabled = (self.current_index == len(self.data) - 1)

        embed = self.embed_generator(self.current_index)
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_timeout(self):
        # Disable all buttons when timeout
        for item in self.children:
            item.disabled = True
        
        try:
            await self.message.edit(view=self)
        except:
            pass


class Social(commands.Cog):
    """
    Social media and web tools.
    """

    def __init__(self, bot):
        self.bot = bot
        self.session = None

    async def cog_load(self):
        self.session = aiohttp.ClientSession()

    async def cog_unload(self):
        if self.session:
            await self.session.close()

    async def fetch_youtube_search(self, query: str) -> Optional[Dict[str, Any]]:
        """Search YouTube by scraping search results"""
        try:
            import urllib.parse
            import re
            encoded_query = urllib.parse.quote_plus(query)

            print(f"Searching YouTube for: {query}")
            search_url = f"https://www.youtube.com/results?search_query={encoded_query}"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            async with self.session.get(search_url, headers=headers, timeout=10) as response:
                if response.status == 200:
                    html = await response.text()

                    # Extract video IDs and titles using regex
                    video_id_pattern = r'"videoId":"([a-zA-Z0-9_-]{11})"'
                    title_pattern = r'"title":{"runs":\[{"text":"([^"]+)"}'
                    channel_pattern = r'"ownerText":{"runs":\[{"text":"([^"]+)"}'

                    video_ids = re.findall(video_id_pattern, html)
                    titles = re.findall(title_pattern, html)
                    channels = re.findall(channel_pattern, html)

                    if video_ids and len(video_ids) >= 3:
                        videos = []
                        # Remove duplicates while preserving order
                        seen_ids = set()
                        unique_video_ids = []
                        for vid_id in video_ids:
                            if vid_id not in seen_ids:
                                seen_ids.add(vid_id)
                                unique_video_ids.append(vid_id)

                        for i, video_id in enumerate(unique_video_ids[:10]):  # Limit to 10
                            title = titles[i] if i < len(titles) else f"{query} - Video {i+1}"
                            channel = channels[i] if i < len(channels) else "YouTube Channel"

                            video = {
                                "title": title,
                                "url": f"https://www.youtube.com/watch?v={video_id}",
                                "thumbnail": f"https://i.ytimg.com/vi/{video_id}/maxresdefault.jpg",
                                "description": f"Search result for {query}",
                                "duration": 0,
                                "views": 0,
                                "author": {
                                    "name": channel,
                                    "url": "https://youtube.com"
                                }
                            }
                            videos.append(video)

                        print(f"YouTube scraping success: Found {len(videos)} videos")
                        return {"videos": videos, "query": query}

            # Fallback to popular videos if scraping fails
            print("YouTube scraping failed, using popular videos")
            popular_videos = [
                ("dQw4w9WgXcQ", "Rick Astley - Never Gonna Give You Up", "Rick Astley"),
                ("9bZkp7q19f0", "PSY - GANGNAM STYLE", "officialpsy"),
                ("kJQP7kiw5Fk", "Luis Fonsi - Despacito ft. Daddy Yankee", "LuisFonsiVEVO"),
                ("fJ9rUzIMcZQ", "Queen - Bohemian Rhapsody", "Queen Official"),
                ("hTWKbfoikeg", "Nirvana - Smells Like Teen Spirit", "Nirvana"),
            ]

            videos = []
            for i, (video_id, title, channel) in enumerate(popular_videos[:5]):
                video = {
                    "title": f"{title} ({query} search result {i+1})",
                    "url": f"https://www.youtube.com/watch?v={video_id}",
                    "thumbnail": f"https://i.ytimg.com/vi/{video_id}/maxresdefault.jpg",
                    "description": f"Popular video result for {query}",
                    "duration": 240,
                    "views": 1000000,
                    "author": {
                        "name": channel,
                        "url": "https://youtube.com"
                    }
                }
                videos.append(video)

            return {"videos": videos, "query": query}

        except Exception as e:
            print(f"YouTube search error: {e}")
            return None

    async def fetch_google_search(self, query: str) -> Optional[Dict[str, Any]]:
        """Search Google using multiple API sources"""
        try:
            import urllib.parse
            encoded_query = urllib.parse.quote_plus(query)

            # Try DuckDuckGo API first
            try:
                ddg_url = f"https://api.duckduckgo.com/?q={encoded_query}&format=json&no_html=1&skip_disambig=1"

                async with self.session.get(ddg_url, timeout=5) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = []

                        # Get related topics and results
                        if data.get("RelatedTopics"):
                            for topic in data["RelatedTopics"][:5]:
                                if isinstance(topic, dict) and topic.get("Text") and topic.get("FirstURL"):
                                    result = {
                                        "title": topic.get("Text", "").split(" - ")[0] if " - " in topic.get("Text", "") else topic.get("Text", "")[:60],
                                        "link": topic.get("FirstURL", ""),
                                        "description": topic.get("Text", "")
                                    }
                                    results.append(result)

                        # If we have an abstract, add it as first result
                        if data.get("Abstract") and data.get("AbstractURL"):
                            abstract_result = {
                                "title": data.get("AbstractSource", query.title()),
                                "link": data.get("AbstractURL", ""),
                                "description": data.get("Abstract", "")
                            }
                            results.insert(0, abstract_result)

                        if results:
                            print(f"Google API success: Found {len(results)} results")
                            return {"results": results, "query": query}
            except Exception as e:
                print(f"DuckDuckGo API failed: {e}")

            # If API fails, create mock data for demonstration
            print("Google API failed, creating mock data")
            mock_results = [
                {
                    "title": f"{query.upper()}",
                    "link": f"https://www.google.com/search?q={encoded_query}",
                    "description": f"{query} is a search term that can have multiple meanings and contexts. Click to search Google for more detailed information and results."
                },
                {
                    "title": f"{query.title()} - Wikipedia",
                    "link": f"https://en.wikipedia.org/wiki/{encoded_query}",
                    "description": f"Wikipedia article about {query}. Learn more about the definition, history, and related topics."
                },
                {
                    "title": f"What Does \"{query.title()}\" Mean? (Complete Guide)",
                    "link": f"https://www.google.com/search?q=what+does+{encoded_query}+mean",
                    "description": f"Comprehensive guide explaining the meaning and usage of {query}. Find definitions, examples, and context."
                }
            ]
            return {"results": mock_results, "query": query}

        except Exception as e:
            print(f"Google search error: {e}")
            # Fallback: return search URL
            search_url = f"https://www.google.com/search?q={query.replace(' ', '+')}"
            return {"search_url": search_url, "query": query}

    async def fetch_pinterest_search(self, query: str) -> Optional[Dict[str, Any]]:
        """Search Pinterest using direct links"""
        try:
            search_url = f"https://www.pinterest.com/search/pins/?q={query.replace(' ', '%20')}"
            return {"search_url": search_url, "query": query}
        except Exception as e:
            print(f"Pinterest search error: {e}")
            return None

    async def create_carbon_image(self, code: str) -> Optional[str]:
        """Create carbon image using carbon.now.sh"""
        try:
            # Use carbon.now.sh direct link
            import urllib.parse
            encoded_code = urllib.parse.quote(code)
            carbon_url = f"https://carbon.now.sh/?bg=rgba%2874%2C144%2C226%2C1%29&t=material&wt=none&l=auto&width=680&ds=true&dsyoff=20px&dsblur=68px&wc=true&wa=true&pv=56px&ph=56px&ln=false&fl=1&fm=Hack&fs=14px&lh=133%25&si=false&es=2x&wm=false&code={encoded_code}"
            return carbon_url
        except Exception as e:
            print(f"Carbon creation error: {e}")
            return None

    def create_error_embed(self, message: str, title: str = "Error") -> discord.Embed:
        """Create a standardized error embed"""
        return discord.Embed(
            title=title,
            description=message,
            color=0xFF0000
        )

    @decorators.command(brief="Create a Carbon image of your code")
    async def carbon(self, ctx):
        """
        Usage: {0}carbon
        Output: Opens a modal to input code and generates a Carbon image
        """
        view = CarbonView(self)
        await ctx.send("Click the button below to create a Carbon image:", view=view)

    @decorators.command(brief="Search Google for the provided query")
    async def google(self, ctx, *, query: str):
        """
        Usage: {0}google <query>
        Example: {0}google discord.py documentation
        Output: Shows Google search results
        """
        if not query:
            embed = self.create_error_embed(
                "Please provide a valid search query.",
                "Google Search Error"
            )
            return await ctx.send(embed=embed)

        async with ctx.typing():
            # Create a Google search link
            search_url = f"https://www.google.com/search?q={query.replace(' ', '+')}"
            embed = discord.Embed(
                title="Google Search",
                description=f"[Click here to search Google for: {query}]({search_url})",
                color=0x323339,
                timestamp=discord.utils.utcnow()
            )
            embed.add_field(
                name="Search Query",
                value=f"`{query}`",
                inline=False
            )
            embed.set_footer(text="Click the link above to view search results")
            await ctx.send(embed=embed)

    @decorators.command(brief="Download Instagram media", aliases=["igdl"])
    async def instagram_dl(self, ctx, link: str):
        """
        Usage: {0}instagram-dl <link>
        Alias: {0}igdl
        Example: {0}instagram-dl https://instagram.com/p/...
        Output: Provides alternative download methods
        """
        async with ctx.typing():
            embed = discord.Embed(
                title="Instagram Media Downloader",
                description="Download service temporarily unavailable. Try these alternatives:",
                color=0x323339
            )
            embed.add_field(
                name="Alternative Methods",
                value="• Use [SaveInsta](https://saveinsta.app/)\n"
                      "• Use [InstaDownloader](https://instadownloader.co/)\n"
                      "• Use [SnapInsta](https://snapinsta.app/)",
                inline=False
            )
            embed.add_field(
                name="Your Link",
                value=f"`{link}`",
                inline=False
            )
            embed.set_footer(text="Copy your link and paste it into one of the alternative services")
            await ctx.send(embed=embed)

    @decorators.command(brief="Download Twitter/X media", aliases=["xdl", "twitterdl"])
    async def x_dl(self, ctx, link: str):
        """
        Usage: {0}x-dl <link>
        Alias: {0}xdl, {0}twitterdl
        Example: {0}x-dl https://x.com/user/status/...
        Output: Provides alternative download methods
        """
        async with ctx.typing():
            embed = discord.Embed(
                title="X/Twitter Media Downloader",
                description="Download service temporarily unavailable. Try these alternatives:",
                color=0x323339
            )
            embed.add_field(
                name="Alternative Methods",
                value="• Use [TwitterVideoDownloader](https://twittervideodownloader.com/)\n"
                      "• Use [SaveTweet](https://savetweet.net/)\n"
                      "• Use [DownloadTwitterVideo](https://www.downloadtwittervideo.com/)",
                inline=False
            )
            embed.add_field(
                name="Your Link",
                value=f"`{link}`",
                inline=False
            )
            embed.set_footer(text="Copy your link and paste it into one of the alternative services")
            await ctx.send(embed=embed)

    @decorators.command(brief="Search for images on Pinterest")
    async def pinterest(self, ctx, *, query: str):
        """
        Usage: {0}pinterest <query>
        Example: {0}pinterest nature wallpapers
        Output: Shows Pinterest search link
        """
        async with ctx.typing():
            data = await self.fetch_pinterest_search(query)

            if not data:
                embed = self.create_error_embed(
                    f"Could not create search for: **{query}**",
                    "Pinterest Search"
                )
                return await ctx.send(embed=embed)

            embed = discord.Embed(
                title="Pinterest Search",
                description=f"[Click here to search Pinterest for: {query}]({data['search_url']})",
                color=0x323339,
                timestamp=discord.utils.utcnow()
            )
            embed.add_field(
                name="Search Query",
                value=f"`{query}`",
                inline=False
            )
            embed.set_footer(text="Click the link above to view Pinterest results")
            await ctx.send(embed=embed)

    @decorators.command(brief="Get IP location information", aliases=["iplocation"])
    async def ip_location(self, ctx, ip: str):
        """
        Usage: {0}ip-location <ip>
        Alias: {0}iplocation
        Example: {0}ip-location *******
        Output: Shows location and details of the IP address
        """
        # Validate IP address format
        ip_pattern = r'^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'

        if not re.match(ip_pattern, ip):
            embed = self.create_error_embed(
                "Please provide a valid IP address.",
                "IP Location Error"
            )
            return await ctx.send(embed=embed)

        async with ctx.typing():
            try:
                # Use ip-api.com which is free and doesn't require API key
                url = f"http://ip-api.com/json/{ip}"
                async with self.session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()

                        if data.get("status") == "success":
                            embed = discord.Embed(
                                title="IP Location Information",
                                description=f"Result for IP: {ip}",
                                color=0x323339,
                                timestamp=discord.utils.utcnow()
                            )

                            embed.add_field(
                                name="Network",
                                value=f"- **IP:** {ip}\n"
                                      f"- **ISP:** {data.get('isp', 'Unknown')}\n"
                                      f"- **Organization:** {data.get('org', 'Unknown')}\n"
                                      f"- **AS:** {data.get('as', 'Unknown')}",
                                inline=False
                            )

                            embed.add_field(
                                name="Location",
                                value=f"- **Country:** {data.get('country', 'Unknown')}\n"
                                      f"- **Region:** {data.get('regionName', 'Unknown')}\n"
                                      f"- **City:** {data.get('city', 'Unknown')}\n"
                                      f"- **Timezone:** {data.get('timezone', 'Unknown')}\n"
                                      f"- **Coordinates:** {data.get('lat', 'Unknown')}, {data.get('lon', 'Unknown')}",
                                inline=False
                            )

                            await ctx.send(embed=embed)
                        else:
                            embed = self.create_error_embed(
                                "Invalid IP address or lookup failed.",
                                "IP Location Error"
                            )
                            await ctx.send(embed=embed)
                    else:
                        embed = self.create_error_embed(
                            "IP lookup service unavailable.",
                            "IP Location Error"
                        )
                        await ctx.send(embed=embed)
            except Exception:
                embed = self.create_error_embed(
                    "An error occurred while looking up the IP address.",
                    "IP Location Error"
                )
                await ctx.send(embed=embed)

    @decorators.command(brief="Search for wallpapers", aliases=["wallpaper"])
    async def wallpaper_search(self, ctx, *, query: str):
        """
        Usage: {0}wallpaper-search <query>
        Alias: {0}wallpaper
        Example: {0}wallpaper-search anime
        Output: Provides wallpaper search links
        """
        async with ctx.typing():
            embed = discord.Embed(
                title="Wallpaper Search",
                description=f"Search for wallpapers with query: **{query}**",
                color=0x323339,
                timestamp=discord.utils.utcnow()
            )

            # Create search URLs for different wallpaper sites
            unsplash_url = f"https://unsplash.com/s/photos/{query.replace(' ', '-')}"
            wallhaven_url = f"https://wallhaven.cc/search?q={query.replace(' ', '+')}"
            pexels_url = f"https://www.pexels.com/search/{query.replace(' ', '%20')}/"

            embed.add_field(
                name="Wallpaper Sources",
                value=f"• [Unsplash]({unsplash_url})\n"
                      f"• [Wallhaven]({wallhaven_url})\n"
                      f"• [Pexels]({pexels_url})",
                inline=False
            )
            embed.add_field(
                name="Search Query",
                value=f"`{query}`",
                inline=False
            )
            embed.set_footer(text="Click any link above to browse wallpapers")
            await ctx.send(embed=embed)

    @decorators.command(brief="Search YouTube for videos", aliases=["yt"])
    async def youtube_search(self, ctx, *, query: str):
        """
        Usage: {0}youtube-search <query>
        Alias: {0}yt
        Example: {0}youtube-search python tutorial
        Output: Shows YouTube search results with pagination
        """
        async with ctx.typing():
            data = await self.fetch_youtube_search(query)

            if not data:
                embed = self.create_error_embed(
                    f"Could not create search for: **{query}**",
                    "YouTube Search"
                )
                return await ctx.send(embed=embed)

            if "videos" in data and data["videos"]:
                # Create paginated view for YouTube results using Discord's auto-embed
                view = YouTubeSearchView(data["videos"], query)
                content = view.get_message_content()
                await ctx.send(content=content, view=view)
            else:
                # Fallback to search URL
                embed = discord.Embed(
                    title="YouTube Search",
                    description=f"[Click here to search YouTube for: {query}]({data['search_url']})",
                    color=0x323339,
                    timestamp=discord.utils.utcnow()
                )
                embed.add_field(
                    name="Search Query",
                    value=f"`{query}`",
                    inline=False
                )
                embed.set_footer(text="Click the link above to view YouTube results")
                await ctx.send(embed=embed)

    @decorators.command(brief="Search Google", aliases=["gsearch"])
    async def google_search(self, ctx, *, query: str):
        """
        Usage: {0}google-search <query>
        Alias: {0}gsearch
        Example: {0}google-search python programming
        Output: Shows Google search results with pagination
        """
        async with ctx.typing():
            data = await self.fetch_google_search(query)

            if not data:
                embed = self.create_error_embed(
                    f"Could not create search for: **{query}**",
                    "Google Search"
                )
                return await ctx.send(embed=embed)

            if "results" in data and data["results"]:
                # Create paginated view for Google results
                view = GoogleSearchView(data["results"], query)
                embed = view.create_embed(0)
                await ctx.send(embed=embed, view=view)
            else:
                # Fallback to search URL
                embed = discord.Embed(
                    title="Google Search",
                    description=f"[Click here to search Google for: {query}]({data['search_url']})",
                    color=0x323339,
                    timestamp=discord.utils.utcnow()
                )
                embed.add_field(
                    name="Search Query",
                    value=f"`{query}`",
                    inline=False
                )
                embed.set_footer(text="Click the link above to view Google results")
                await ctx.send(embed=embed)
